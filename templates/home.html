{% extends "base.html" %}

{% block title %}Accueil{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='home.css') }}">
{% endblock %}

{% block content %}
<div class="home-container">
    <div class="welcome-section">
        <h1>Bienvenue dans l'Application</h1>
        <h2><PERSON><PERSON><PERSON>, {{ user.name }}!</h2>
        <p>
            Vous êtes connecté avec succès à votre compte.
        </p>

        <div class="action-buttons">
            <button type="button" onclick="showLogoutDialog()">Déconnexion</button>
        </div>
    </div>

    <div class="user-info">
        <h3>Informations du compte</h3>
        <p><strong>Email:</strong> {{ user.email }}</p>
        <p><strong>Nom:</strong> {{ user.name }}</p>
    </div>
</div>

<!-- Logout Confirmation Dialog -->
<div id="logoutDialog" class="dialog-overlay">
    <div class="dialog-content">
        <h3>Confirmer la déconnexion</h3>
        <p>Êtes-vous sûr de vouloir vous déconnecter ?</p>
        <div class="dialog-actions">
            <form method="POST" action="{{ url_for('logout') }}" style="display: inline;">
                <button type="submit" class="btn-danger">Oui, me déconnecter</button>
            </form>
            <button type="button" class="btn-cancel" onclick="hideLogoutDialog()">Annuler</button>
        </div>
    </div>
</div>

<script>
function showLogoutDialog() {
    const dialog = document.getElementById('logoutDialog');
    dialog.style.display = 'flex';
    // Trigger animation
    setTimeout(() => {
        dialog.classList.add('show');
    }, 10);

    // Focus trap for accessibility
    const firstButton = dialog.querySelector('.btn-danger');
    firstButton.focus();
}

function hideLogoutDialog() {
    const dialog = document.getElementById('logoutDialog');
    dialog.classList.remove('show');

    // Hide after animation completes
    setTimeout(() => {
        dialog.style.display = 'none';
    }, 300);
}

// Close dialog when clicking outside
document.getElementById('logoutDialog').addEventListener('click', function(e) {
    if (e.target === this) {
        hideLogoutDialog();
    }
});

// Close dialog with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        hideLogoutDialog();
    }
});

// Keyboard navigation within dialog
document.getElementById('logoutDialog').addEventListener('keydown', function(e) {
    if (e.key === 'Tab') {
        const focusableElements = this.querySelectorAll('button');
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }
});
</script>
{% endblock %}
