{% extends "base.html" %}

{% block title %}Inscription - Portail PDF Étudiant{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='signup.css') }}">
{% endblock %}

{% block content %}
<div class="signup-container">
    <h1>Créer un compte</h1>
    <p>Rejoignez EduShare pour commencer à partager vos documents</p>

    <form method="POST" autocomplete="off">
        <div class="form-row">
            <div class="form-group">
                <label for="first_name">Prénom *</label>
                <input type="text" id="first_name" name="first_name" placeholder="Votre prénom" required autocomplete="off" maxlength="50">
            </div>

            <div class="form-group">
                <label for="last_name">Nom *</label>
                <input type="text" id="last_name" name="last_name" placeholder="Votre nom" required autocomplete="off" maxlength="50">
            </div>
        </div>

        <div class="form-group">
            <label for="email">Adresse email *</label>
            <input type="email" id="email" name="email" placeholder="<EMAIL>" required autocomplete="off" maxlength="100">
        </div>

        <div class="form-group">
            <label for="password">Mot de passe *</label>
            <div class="password-container">
                <input type="password" id="password" name="password" placeholder="Créez un mot de passe sécurisé" required autocomplete="new-password" maxlength="200" minlength="8">
                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                    👁️
                </button>
            </div>
            <small>Le mot de passe doit contenir au moins 8 caractères.</small>
        </div>

        <div class="form-group">
            <label for="confirm_password">Confirmer le mot de passe *</label>
            <div class="password-container">
                <input type="password" id="confirm_password" name="confirm_password" placeholder="Confirmez votre mot de passe" required autocomplete="new-password" maxlength="200" minlength="8">
                <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                    👁️
                </button>
            </div>
        </div>

        <button type="submit">Créer mon compte</button>

        <div class="oauth" style="text-align: center; margin-top: 16px; padding-top: 16px; border-top: 1px solid #eee;">
            <a href="{{ url_for('google_login') }}" style="display:inline-block;padding:10px 14px;border:1px solid #ccc;border-radius:6px;text-decoration:none;color:#111;background:#fff">
                🔐 Continuer avec Google
            </a>
        </div>

    </form>

    <div class="login-link">
        Vous avez déjà un compte? <a href="{{ url_for('login') }}">Connectez-vous</a>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;

    if (field.type === 'password') {
        field.type = 'text';
        button.textContent = '🙈';
    } else {
        field.type = 'password';
        button.textContent = '👁️';
    }
}
</script>
{% endblock %}
